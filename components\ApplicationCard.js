import { useState } from "react";
import { useRouter } from "next/router";
import { formatDate, formatCurrency, formatPercentage } from "../lib/utils";
import {
  FileText,
  Check,
  X,
  Clock,
  User,
  Building,
  Calendar,
  DollarSign,
  MessageSquare,
  ExternalLink,
  AlertCircle,
  MoreVertical,
  Edit,
  Eye
} from "lucide-react";

export default function ApplicationCard({ application, user, onUpdate, className = "" }) {
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const handleAction = async (action) => {
    setLoading(true);
    try {
      const res = await fetch(`/api/applications/${application.id}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ action }),
      });

      if (res.ok) {
        const data = await res.json();

        // If application was accepted and contract was generated, show success message and option to view contract
        if (action === 'accept' && data.contractGenerated && data.contractId) {
          const viewContract = window.confirm(
            "Application accepted successfully! A contract has been generated. Would you like to view the contract now?"
          );

          if (viewContract) {
            router.push(`/contract/${data.contractId}`);
            return;
          }
        } else if (action === 'accept') {
          alert("Application accepted successfully!");
        } else if (action === 'reject') {
          alert("Application rejected successfully!");
        }

        onUpdate?.();
      } else {
        const error = await res.json();
        alert(error.error || "Failed to update application");
        console.error("Failed to update application:", error);
      }
    } catch (error) {
      alert("Failed to update application");
      console.error("Failed to update application:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateContract = async () => {
    setLoading(true);
    try {
      const res = await fetch("/api/contracts/create", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ applicationId: application.id }),
      });

      if (res.ok) {
        const data = await res.json();
        const viewContract = window.confirm(
          "Contract generated successfully! Would you like to view the contract now?"
        );

        if (viewContract) {
          router.push(`/contract/${data.contractId}`);
        } else {
          onUpdate?.(); // Refresh the application list
        }
      } else {
        const error = await res.json();
        // If contract already exists, still offer to view it
        if (error.contractId) {
          const viewContract = window.confirm(
            "Contract already exists for this application. Would you like to view it?"
          );
          if (viewContract) {
            router.push(`/contract/${error.contractId}`);
          }
        } else {
          alert(error.error || "Failed to generate contract");
        }
      }
    } catch (error) {
      alert("Failed to generate contract");
      console.error("Failed to generate contract:", error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = () => {
    const statusConfig = {
      pending: { className: "status-pending", icon: Clock, label: "Pending" },
      accepted: { className: "status-accepted", icon: Check, label: "Accepted" },
      rejected: { className: "status-rejected", icon: X, label: "Rejected" },
      in_progress: { className: "status-in-progress", icon: FileText, label: "In Progress" },
      completed: { className: "status-completed", icon: Check, label: "Completed" },
    };

    const config = statusConfig[application.status] || statusConfig.pending;
    const Icon = config.icon;

    return (
      <span className={`${config.className} flex items-center gap-1 mt-3`}>
        <Icon size={12} />
        {config.label}
      </span>
    );
  };

  const getPaymentDisplay = () => {
    if (application.payment_type === "percentage") {
      return `${formatPercentage(application.payment_percentage)}% recurring`;
    }
    return `${formatCurrency(application.payment_fixed)} fixed`;
  };

  const getDaysAgo = () => {
    const days = Math.floor((new Date() - new Date(application.created_at)) / (1000 * 60 * 60 * 24));
    if (days === 0) return "Today";
    if (days === 1) return "Yesterday";
    return `${days} days ago`;
  };

  return (
    <div className={`card card-hover group ${className}`}>
      <div className="card-content">
        {/* Header */}
        <div className="flex justify-between items-start mb-4">
          <div className="flex-1 min-w-0">
            <h4 className="text-lg font-semibold mb-1 truncate group-hover:text-primary transition-colors mt-3">
              {user.user_type === "seeker"
                ? application.referrer_name || "Unknown Referrer"
                : application.job_title || "Unknown Job"}
            </h4>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Calendar size={14} />
              <span>{getDaysAgo()}</span>
              <span>•</span>
              <span>{formatDate(application.created_at)}</span>
            </div>
          </div>
          <div className="flex flex-col items-end gap-2">
            {getStatusBadge()}
            {application.status === "in_progress" && (
              <div className="text-xs text-red-600 bg-red-50 px-2 py-1 rounded mt-3">
                Documents & Verification
              </div>
            )}
          </div>
        </div>

        {/* Application Details */}
        <div className="space-y-3 mb-4">
          {user.user_type === "referrer" && application.seeker_name && (
            <div className="flex items-center gap-2 text-sm">
              <User size={14} className="text-muted-foreground" />
              <span className="text-muted-foreground">Job Seeker:</span>
              <span className="font-medium">{application.seeker_name}</span>
            </div>
          )}

          {application.company_name && (
            <div className="flex items-center gap-2 text-sm">
              <Building size={14} className="text-muted-foreground" />
              <span className="text-muted-foreground">Company:</span>
              <span className="font-medium">{application.company_name}</span>
            </div>
          )}

          {(application.payment_type || application.payment_percentage || application.payment_fixed) && (
            <div className="flex items-center gap-2 text-sm">
              <DollarSign size={14} className="text-muted-foreground" />
              <span className="text-muted-foreground">Payment:</span>
              <span className="font-medium">{getPaymentDisplay()}</span>
            </div>
          )}

          {application.job_role && (
            <div className="flex items-center gap-2">
              <span className="badge-secondary text-xs">{application.job_role}</span>
            </div>
          )}
        </div>

        {/* Message */}
        {application.message && (
          <div className="mb-4">
            <div className="flex items-center gap-2 mb-2">
              <MessageSquare size={14} className="text-muted-foreground" />
              <span className="text-sm font-medium text-muted-foreground">Message:</span>
            </div>
            <p className="text-sm bg-muted p-3 rounded-lg border-l-2 border-border">
              {application.message}
            </p>
          </div>
        )}

        {/* Position Details */}
        {application.position_details && (
          <div className="mb-4">
            <div className="flex items-center gap-2 mb-2">
              <FileText size={14} className="text-muted-foreground" />
              <span className="text-sm font-medium text-muted-foreground">Position Details:</span>
            </div>
            <p className="text-sm bg-muted p-3 rounded-lg">
              {application.position_details}
            </p>
          </div>
        )}

        {/* Actions */}
        <div className="flex flex-col sm:flex-row gap-2 justify-between items-start">
          <div className="flex flex-wrap gap-2">
            {application.status === "pending" && user.user_type === "seeker" && (
              <>
                <button
                  onClick={() => handleAction("accept")}
                  disabled={loading}
                  className="btn-primary btn-sm flex items-center gap-2"
                >
                  {loading ? (
                    <div className="w-3 h-3 border-2 border-primary-foreground border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <Check size={14} />
                  )}
                  Accept
                </button>
                <button
                  onClick={() => handleAction("reject")}
                  disabled={loading}
                  className="btn-outline btn-sm flex items-center gap-2 text-destructive border-destructive hover:bg-destructive hover:text-destructive-foreground"
                >
                  {loading ? (
                    <div className="w-3 h-3 border-2 border-current border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <X size={14} />
                  )}
                  Reject
                </button>
              </>
            )}

            {/* Contract Access Buttons - For Accepted Applications */}
            {application.status === "accepted" && application.contract_id && (
              <button
                onClick={() => router.push(`/contract/${application.contract_id}`)}
                className="btn-primary btn-sm flex items-center gap-2"
              >
                <ExternalLink size={14} />
                View Contract
              </button>
            )}

            {application.status === "accepted" && !application.contract_id && (
              <button
                onClick={() => handleGenerateContract()}
                disabled={loading}
                className="btn-primary btn-sm flex items-center gap-2"
              >
                {loading ? (
                  <div className="w-3 h-3 border-2 border-primary-foreground border-t-transparent rounded-full animate-spin" />
                ) : (
                  <FileText size={14} />
                )}
                Generate Contract
              </button>
            )}

            {/* Contract Access Buttons - For In Progress Applications */}
            {application.status === "in_progress" && application.contract_id && (
              <div className="flex flex-col sm:flex-row gap-2">
                <button
                  onClick={() => router.push(`/contract/${application.contract_id}`)}
                  className="btn-primary btn-sm flex items-center justify-center gap-2 w-full sm:w-auto"
                >
                  <FileText size={14} />
                  Continue Contract
                </button>
                <div className="flex gap-2 w-full sm:w-auto">
                  <button
                    onClick={() => router.push(`/contract/${application.contract_id}?tab=documents`)}
                    className="btn-outline btn-sm flex items-center justify-center gap-2 flex-1 sm:flex-none"
                    title="Upload Documents"
                  >
                    <ExternalLink size={14} />
                    Documents
                  </button>
                  <button
                    onClick={() => router.push(`/contract/${application.contract_id}?tab=contract`)}
                    className="btn-outline btn-sm flex items-center justify-center gap-2 flex-1 sm:flex-none"
                    title="View Contract Details"
                  >
                    <Eye size={14} />
                    View
                  </button>
                </div>
              </div>
            )}

            {/* Contract Access Buttons - For Completed Applications */}
            {application.status === "completed" && application.contract_id && (
              <div className="flex gap-2">
                <button
                  onClick={() => router.push(`/contract/${application.contract_id}`)}
                  className="btn-outline btn-sm flex items-center gap-2"
                >
                  <FileText size={14} />
                  View Contract
                </button>
                <div className="flex items-center gap-2 text-sm text-green-600">
                  <Check size={14} />
                  <span>Completed</span>
                </div>
              </div>
            )}

            {/* Contract Pending State */}
            {application.status === "accepted" && !application.contract_id && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <AlertCircle size={14} />
                <span>Contract pending</span>
              </div>
            )}

            {/* Missing Contract for In Progress */}
            {application.status === "in_progress" && !application.contract_id && (
              <div className="flex gap-2">
                <button
                  onClick={() => handleGenerateContract()}
                  disabled={loading}
                  className="btn-primary btn-sm flex items-center gap-2"
                >
                  {loading ? (
                    <div className="w-3 h-3 border-2 border-primary-foreground border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <FileText size={14} />
                  )}
                  Generate Contract
                </button>
                <div className="flex items-center gap-2 text-sm text-orange-600">
                  <AlertCircle size={14} />
                  <span>Contract missing</span>
                </div>
              </div>
            )}
          </div>

          {/* Additional Info */}
          <div className="text-xs text-muted-foreground">
            ID: {application.id}
          </div>
        </div>
      </div>
    </div>
  );
}
