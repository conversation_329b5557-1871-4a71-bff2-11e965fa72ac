import { withAuth } from "../../../lib/middleware";
import { query } from "../../../lib/db";
import formidable from "formidable";
import fs from "fs";
import path from "path";

export const config = {
  api: {
    bodyParser: false,
  },
};

export default withAuth(async (req, res) => {
  if (req.method === "GET") {
    // Get user's profile documents
    try {
      const documents = await query(
        `SELECT 
          document_type, 
          document_url, 
          is_verified, 
          uploaded_at,
          updated_at
        FROM profile_documents 
        WHERE user_id = ?
        ORDER BY document_type`,
        [req.user.id]
      );

      // Convert to object format for easier frontend handling
      const documentsObj = {};
      documents.forEach(doc => {
        documentsObj[doc.document_type] = {
          url: doc.document_url,
          verified: doc.is_verified,
          uploadedAt: doc.uploaded_at,
          updatedAt: doc.updated_at
        };
      });

      res.status(200).json({ documents: documentsObj });
    } catch (error) {
      console.error("Failed to fetch profile documents:", error);
      res.status(500).json({ error: "Failed to fetch documents" });
    }
  } else if (req.method === "POST") {
    // Upload or update a profile document
    try {
      // Create upload directory if it doesn't exist
      const uploadDir = "./public/uploads/profile";
      if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
      }

      // Parse multipart form data
      const chunks = [];
      req.on('data', (chunk) => {
        chunks.push(chunk);
      });

      req.on('end', async () => {
        try {
          const buffer = Buffer.concat(chunks);
          const boundary = req.headers['content-type'].split('boundary=')[1];
          
          if (!boundary) {
            return res.status(400).json({ error: "Invalid multipart data" });
          }

          // Parse multipart data manually (simplified)
          const parts = buffer.toString().split(`--${boundary}`);
          let documentType = null;
          let fileData = null;
          let fileName = null;
          let mimeType = null;

          for (const part of parts) {
            if (part.includes('name="documentType"')) {
              const match = part.match(/\r\n\r\n([^\r\n]+)/);
              if (match) documentType = match[1].trim();
            } else if (part.includes('name="document"')) {
              const headerEnd = part.indexOf('\r\n\r\n');
              if (headerEnd !== -1) {
                const headers = part.substring(0, headerEnd);
                const filenameMatch = headers.match(/filename="([^"]+)"/);
                const contentTypeMatch = headers.match(/Content-Type:\s*([^\r\n]+)/);
                
                if (filenameMatch) fileName = filenameMatch[1];
                if (contentTypeMatch) mimeType = contentTypeMatch[1].trim();
                
                const fileContent = part.substring(headerEnd + 4);
                const endBoundary = fileContent.lastIndexOf('\r\n--');
                if (endBoundary !== -1) {
                  fileData = Buffer.from(fileContent.substring(0, endBoundary), 'binary');
                }
              }
            }
          }

          if (!documentType || !fileData || !fileName) {
            return res.status(400).json({ error: "Missing required fields" });
          }

          // Validate document type
          const allowedTypes = ['aadhar', 'pan', 'driving_license', 'passport'];
          if (!allowedTypes.includes(documentType)) {
            return res.status(400).json({ error: "Invalid document type" });
          }

          // Validate file type
          const allowedMimeTypes = [
            "image/jpeg",
            "image/png", 
            "image/jpg",
            "application/pdf",
          ];
          if (!allowedMimeTypes.includes(mimeType)) {
            return res.status(400).json({ error: "Invalid file type. Please upload JPG, PNG, or PDF files." });
          }

          // Validate file size (5MB max)
          if (fileData.length > 5 * 1024 * 1024) {
            return res.status(400).json({ error: "File size must be less than 5MB" });
          }

          // Generate unique filename
          const ext = path.extname(fileName || '');
          const uniqueFilename = `${req.user.id}-${documentType}-${Date.now()}${ext}`;
          const filePath = path.join(uploadDir, uniqueFilename);

          // Save file to disk
          fs.writeFileSync(filePath, fileData);

          const documentUrl = `/uploads/profile/${uniqueFilename}`;

          // Check if document already exists for this user and type
          const existingDocs = await query(
            "SELECT id FROM profile_documents WHERE user_id = ? AND document_type = ?",
            [req.user.id, documentType]
          );

          if (existingDocs.length > 0) {
            // Update existing document
            await query(
              `UPDATE profile_documents 
               SET document_url = ?, is_verified = FALSE, updated_at = NOW()
               WHERE user_id = ? AND document_type = ?`,
              [documentUrl, req.user.id, documentType]
            );
          } else {
            // Insert new document
            await query(
              `INSERT INTO profile_documents (user_id, document_type, document_url)
               VALUES (?, ?, ?)`,
              [req.user.id, documentType, documentUrl]
            );
          }

          res.status(200).json({
            success: true,
            url: documentUrl,
            message: "Document uploaded successfully"
          });

        } catch (error) {
          console.error("Failed to process upload:", error);
          res.status(500).json({
            error: "Failed to process upload",
            details: error.message
          });
        }
      });

    } catch (error) {
      console.error("Upload error:", error);
      res.status(500).json({ error: "Failed to upload document" });
    }
  } else if (req.method === "DELETE") {
    // Delete a profile document
    try {
      const { documentType } = req.query;

      if (!documentType) {
        return res.status(400).json({ error: "Document type is required" });
      }

      // Get document info before deletion
      const documents = await query(
        "SELECT document_url FROM profile_documents WHERE user_id = ? AND document_type = ?",
        [req.user.id, documentType]
      );

      if (documents.length === 0) {
        return res.status(404).json({ error: "Document not found" });
      }

      // Delete from database
      await query(
        "DELETE FROM profile_documents WHERE user_id = ? AND document_type = ?",
        [req.user.id, documentType]
      );

      // Delete file from disk (optional - you might want to keep files for audit)
      try {
        const filePath = path.join("./public", documents[0].document_url);
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        }
      } catch (fileError) {
        console.warn("Failed to delete file from disk:", fileError);
      }

      res.status(200).json({ 
        success: true, 
        message: "Document deleted successfully" 
      });

    } catch (error) {
      console.error("Failed to delete document:", error);
      res.status(500).json({ error: "Failed to delete document" });
    }
  } else {
    res.status(405).json({ error: "Method not allowed" });
  }
});
