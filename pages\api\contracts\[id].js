import { query } from "../../../lib/db";

export default async function handler(req, res) {
  const { id } = req.query;

  if (req.method === "GET") {
    try {
      const contracts = await query(
        `
        SELECT 
          c.*,
          a.job_post_id,
          a.referrer_id,
          jp.title as job_title,
          jp.payment_type,
          jp.payment_percentage,
          jp.payment_fixed,
          jp.user_id as seeker_id,
          seeker.full_name as seeker_name,
          seeker.email as seeker_email,
          referrer.full_name as referrer_name,
          referrer.email as referrer_email,
          a.company_name
        FROM contracts c
        JOIN applications a ON c.application_id = a.id
        JOIN job_posts jp ON a.job_post_id = jp.id
        JOIN users seeker ON jp.user_id = seeker.id
        JOIN users referrer ON a.referrer_id = referrer.id
        WHERE c.id = ?
      `,
        [id]
      );

      if (contracts.length === 0) {
        return res.status(404).json({ error: "Contract not found" });
      }

      const contract = contracts[0];

      // Convert MySQL boolean fields (0/1) to proper booleans
      contract.seeker_signed = Boolean(contract.seeker_signed);
      contract.referrer_signed = Boolean(contract.referrer_signed);
      contract.seeker_documents_verified = Boolean(contract.seeker_documents_verified);
      contract.referrer_documents_verified = Boolean(contract.referrer_documents_verified);
      contract.offer_letter_released = Boolean(contract.offer_letter_released);

      res.status(200).json({ contract });
    } catch (error) {
      console.error("Failed to fetch contract:", error);
      res.status(500).json({ error: "Failed to fetch contract" });
    }
  } else {
    res.status(405).json({ error: "Method not allowed" });
  }
}
